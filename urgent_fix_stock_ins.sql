-- 紧急修复 stock_ins 表的财务字段
-- 请在 SQL Server Management Studio 中执行此脚本

USE [StudentsCMSSP]
GO

PRINT '开始紧急修复 stock_ins 表...'

-- 添加缺失的字段
BEGIN TRY
    -- 添加 area_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'area_id')
    BEGIN
        ALTER TABLE stock_ins ADD area_id INT NULL;
        PRINT '✓ 添加 area_id 字段'
    END

    -- 添加 supplier_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'supplier_id')
    BEGIN
        ALTER TABLE stock_ins ADD supplier_id INT NULL;
        PRINT '✓ 添加 supplier_id 字段'
    END

    -- 添加 payable_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'payable_id')
    BEGIN
        ALTER TABLE stock_ins ADD payable_id INT NULL;
        PRINT '✓ 添加 payable_id 字段'
    END

    -- 添加 voucher_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'voucher_id')
    BEGIN
        ALTER TABLE stock_ins ADD voucher_id INT NULL;
        PRINT '✓ 添加 voucher_id 字段'
    END

    -- 添加 is_financial_confirmed 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'is_financial_confirmed')
    BEGIN
        ALTER TABLE stock_ins ADD is_financial_confirmed BIT NOT NULL DEFAULT 0;
        PRINT '✓ 添加 is_financial_confirmed 字段'
    END

    -- 添加 total_cost 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'total_cost')
    BEGIN
        ALTER TABLE stock_ins ADD total_cost DECIMAL(10,2) NULL;
        PRINT '✓ 添加 total_cost 字段'
    END

    -- 添加 financial_confirmed_at 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_at')
    BEGIN
        ALTER TABLE stock_ins ADD financial_confirmed_at DATETIME2(1) NULL;
        PRINT '✓ 添加 financial_confirmed_at 字段'
    END

    -- 添加 financial_confirmed_by 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_by')
    BEGIN
        ALTER TABLE stock_ins ADD financial_confirmed_by INT NULL;
        PRINT '✓ 添加 financial_confirmed_by 字段'
    END

    PRINT '字段添加完成，开始初始化数据...'

    -- 为现有数据设置 area_id（从 warehouse 表获取）
    UPDATE si 
    SET area_id = w.area_id
    FROM stock_ins si
    INNER JOIN warehouses w ON si.warehouse_id = w.id
    WHERE si.area_id IS NULL;
    PRINT '✓ 更新现有数据的 area_id'

    -- 为现有数据计算 total_cost（从入库明细计算）
    UPDATE si
    SET total_cost = ISNULL((
        SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
        FROM stock_in_items sii
        WHERE sii.stock_in_id = si.id
    ), 0)
    WHERE si.total_cost IS NULL OR si.total_cost = 0;
    PRINT '✓ 计算现有数据的 total_cost'

    -- 为现有数据设置 supplier_id（从第一个入库明细获取）
    UPDATE si
    SET supplier_id = (
        SELECT TOP 1 sii.supplier_id
        FROM stock_in_items sii
        WHERE sii.stock_in_id = si.id
        AND sii.supplier_id IS NOT NULL
    )
    WHERE si.supplier_id IS NULL;
    PRINT '✓ 设置现有数据的 supplier_id'

    -- 添加外键约束
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_area_id')
    BEGIN
        ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_area_id 
            FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        PRINT '✓ 添加 area_id 外键约束'
    END

    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_supplier_id')
    BEGIN
        ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_supplier_id 
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
        PRINT '✓ 添加 supplier_id 外键约束'
    END

    -- 创建索引
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_area_id')
    BEGIN
        CREATE INDEX IX_stock_ins_area_id ON stock_ins(area_id);
        PRINT '✓ 创建 area_id 索引'
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_is_financial_confirmed')
    BEGIN
        CREATE INDEX IX_stock_ins_is_financial_confirmed ON stock_ins(is_financial_confirmed);
        PRINT '✓ 创建 is_financial_confirmed 索引'
    END

    PRINT ''
    PRINT '🎉 stock_ins 表修复完成！'
    PRINT '现在可以正常使用财务模块了。'

END TRY
BEGIN CATCH
    PRINT '❌ 修复过程中出现错误:'
    PRINT ERROR_MESSAGE()
    PRINT '请检查错误信息并重试。'
END CATCH

GO
