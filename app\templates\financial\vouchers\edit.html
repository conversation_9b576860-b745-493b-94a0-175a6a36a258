{% extends "financial/base.html" %}

{% block title %}编辑财务凭证{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑财务凭证</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> 查看
                        </a>
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 凭证基本信息编辑 -->
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>凭证号</label>
                                    <input type="text" class="form-control" value="{{ voucher.voucher_number }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.voucher_date.label(class="form-label") }}
                                    {{ form.voucher_date(class="form-control") }}
                                    {% if form.voucher_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.voucher_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.voucher_type.label(class="form-label") }}
                                    {{ form.voucher_type(class="form-control") }}
                                    {% if form.voucher_type.errors %}
                                        <div class="text-danger">
                                            {% for error in form.voucher_type.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            {{ form.summary.label(class="form-label") }}
                            {{ form.summary(class="form-control") }}
                            {% if form.summary.errors %}
                                <div class="text-danger">
                                    {% for error in form.summary.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 更新凭证
                            </button>
                            <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <!-- 凭证明细 -->
                    <h5>凭证明细</h5>
                    {% if details %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>行号</th>
                                    <th>会计科目</th>
                                    <th>摘要</th>
                                    <th class="text-right">借方金额</th>
                                    <th class="text-right">贷方金额</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for detail in details %}
                                <tr>
                                    <td>{{ detail.line_number }}</td>
                                    <td>{{ detail.accounting_subject.code }} - {{ detail.accounting_subject.name if detail.accounting_subject else '未知科目' }}</td>
                                    <td>{{ detail.summary or '' }}</td>
                                    <td class="text-right">{{ "%.2f"|format(detail.debit_amount) if detail.debit_amount > 0 else '' }}</td>
                                    <td class="text-right">{{ "%.2f"|format(detail.credit_amount) if detail.credit_amount > 0 else '' }}</td>
                                    <td>
                                        <button class="btn btn-warning btn-sm" onclick="editDetail({{ detail.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteDetail({{ detail.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                                <tr class="font-weight-bold">
                                    <td colspan="3" class="text-right">合计：</td>
                                    <td class="text-right">{{ "%.2f"|format(details|sum(attribute='debit_amount')) }}</td>
                                    <td class="text-right">{{ "%.2f"|format(details|sum(attribute='credit_amount')) }}</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 暂无凭证明细，请添加明细后再提交审核
                    </div>
                    {% endif %}
                    
                    <div class="mt-3">
                        <button class="btn btn-success btn-sm" onclick="addDetail()">
                            <i class="fas fa-plus"></i> 添加明细
                        </button>
                        {% if details and voucher.status == '草稿' %}
                        <button class="btn btn-warning btn-sm" onclick="submitForReview()">
                            <i class="fas fa-paper-plane"></i> 提交审核
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function addDetail() {
    alert('添加明细功能待实现');
}

function editDetail(detailId) {
    alert('编辑明细功能待实现，明细ID: ' + detailId);
}

function deleteDetail(detailId) {
    if (confirm('确定要删除此明细吗？')) {
        alert('删除明细功能待实现，明细ID: ' + detailId);
    }
}

function submitForReview() {
    if (confirm('确定要提交审核吗？提交后将无法修改明细。')) {
        alert('提交审核功能待实现');
    }
}
</script>
{% endblock %}
