"""
财务系统模型
基于入库单的财务核算体系
"""

from datetime import datetime, date
from app import db
from app.models import StandardModel
from sqlalchemy.dialects.mssql import DATETIME2
from sqlalchemy import text


class AccountingSubject(StandardModel):
    """会计科目表"""
    __tablename__ = 'accounting_subjects'

    code = db.Column(db.String(20), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('accounting_subjects.id'), nullable=True)
    level = db.Column(db.Integer, nullable=False, default=1)
    subject_type = db.Column(db.String(20), nullable=False)  # 资产/负债/所有者权益/收入/费用
    balance_direction = db.Column(db.String(10), nullable=False)  # 借方/贷方
    area_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('administrative_areas.id'), nullable=False)
    is_system = db.Column(db.<PERSON>olean, nullable=False, default=False)
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    description = db.Column(db.String(500), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # 关系
    parent = db.relationship('AccountingSubject', remote_side=lambda: [AccountingSubject.id], backref='children')
    area = db.relationship('AdministrativeArea', backref='accounting_subjects')
    creator = db.relationship('User', backref='created_accounting_subjects')

    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('area_id', 'code', name='uq_accounting_subjects_area_code'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'parent_id': self.parent_id,
            'level': self.level,
            'subject_type': self.subject_type,
            'balance_direction': self.balance_direction,
            'area_id': self.area_id,
            'is_system': self.is_system,
            'is_active': self.is_active,
            'description': self.description,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }


class FinancialVoucher(StandardModel):
    """财务凭证表"""
    __tablename__ = 'financial_vouchers'

    voucher_number = db.Column(db.String(50), nullable=False)
    voucher_date = db.Column(db.Date, nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    voucher_type = db.Column(db.String(20), nullable=False)  # 入库凭证/出库凭证/收款凭证/付款凭证/转账凭证
    summary = db.Column(db.String(200), nullable=False)
    total_amount = db.Column(db.Numeric(12, 2), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='草稿')  # 草稿/待审核/已审核/已记账
    source_type = db.Column(db.String(30), nullable=False)  # 入库单/出库单/收款单/付款单/手工录入
    source_id = db.Column(db.Integer, nullable=True)
    attachment_count = db.Column(db.Integer, nullable=False, default=0)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    reviewed_at = db.Column(DATETIME2(precision=1), nullable=True)
    posted_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    posted_at = db.Column(DATETIME2(precision=1), nullable=True)
    notes = db.Column(db.Text, nullable=True)

    # 关系
    area = db.relationship('AdministrativeArea', backref='financial_vouchers')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_vouchers')
    reviewer = db.relationship('User', foreign_keys=[reviewed_by], backref='reviewed_vouchers')
    poster = db.relationship('User', foreign_keys=[posted_by], backref='posted_vouchers')
    details = db.relationship('VoucherDetail', backref='voucher', lazy='dynamic', cascade='all, delete-orphan')

    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('area_id', 'voucher_number', name='uq_financial_vouchers_area_number'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'voucher_number': self.voucher_number,
            'voucher_date': self.voucher_date.strftime('%Y-%m-%d') if self.voucher_date else None,
            'area_id': self.area_id,
            'voucher_type': self.voucher_type,
            'summary': self.summary,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'status': self.status,
            'source_type': self.source_type,
            'source_id': self.source_id,
            'attachment_count': self.attachment_count,
            'created_by': self.created_by,
            'reviewed_by': self.reviewed_by,
            'reviewed_at': self.reviewed_at.strftime('%Y-%m-%d %H:%M:%S') if self.reviewed_at else None,
            'posted_by': self.posted_by,
            'posted_at': self.posted_at.strftime('%Y-%m-%d %H:%M:%S') if self.posted_at else None,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }


class VoucherDetail(db.Model):
    """凭证明细表"""
    __tablename__ = 'voucher_details'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    voucher_id = db.Column(db.Integer, db.ForeignKey('financial_vouchers.id'), nullable=False)
    line_number = db.Column(db.Integer, nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('accounting_subjects.id'), nullable=False)
    summary = db.Column(db.String(200), nullable=False)
    debit_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    credit_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    auxiliary_info = db.Column(db.String(200), nullable=True)
    created_at = db.Column(DATETIME2(precision=1), nullable=False, default=lambda: datetime.now().replace(microsecond=0))

    # 关系
    subject = db.relationship('AccountingSubject', backref='voucher_details')

    def to_dict(self):
        return {
            'id': self.id,
            'voucher_id': self.voucher_id,
            'line_number': self.line_number,
            'subject_id': self.subject_id,
            'subject_name': self.subject.name if self.subject else '',
            'summary': self.summary,
            'debit_amount': float(self.debit_amount) if self.debit_amount else 0,
            'credit_amount': float(self.credit_amount) if self.credit_amount else 0,
            'auxiliary_info': self.auxiliary_info
        }


class AccountPayable(StandardModel):
    """应付账款表（基于入库单）"""
    __tablename__ = 'account_payables'

    payable_number = db.Column(db.String(50), nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    stock_in_id = db.Column(db.Integer, db.ForeignKey('stock_ins.id'), nullable=False)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=True)
    original_amount = db.Column(db.Numeric(12, 2), nullable=False)
    paid_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0)
    balance_amount = db.Column(db.Numeric(12, 2), nullable=False)
    due_date = db.Column(db.Date, nullable=True)
    status = db.Column(db.String(20), nullable=False, default='未付款')  # 未付款/部分付款/已付款/已核销
    payment_terms = db.Column(db.String(200), nullable=True)
    invoice_number = db.Column(db.String(50), nullable=True)
    invoice_date = db.Column(db.Date, nullable=True)
    invoice_amount = db.Column(db.Numeric(12, 2), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    notes = db.Column(db.Text, nullable=True)

    # 关系
    area = db.relationship('AdministrativeArea', backref='account_payables')
    supplier = db.relationship('Supplier', backref='account_payables')
    stock_in = db.relationship('StockIn', backref='account_payables')
    purchase_order = db.relationship('PurchaseOrder', backref='account_payables')
    creator = db.relationship('User', backref='created_payables')
    payment_records = db.relationship('PaymentRecord', backref='payable', lazy='dynamic')

    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('area_id', 'payable_number', name='uq_account_payables_area_number'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'payable_number': self.payable_number,
            'area_id': self.area_id,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else '',
            'stock_in_id': self.stock_in_id,
            'purchase_order_id': self.purchase_order_id,
            'original_amount': float(self.original_amount) if self.original_amount else 0,
            'paid_amount': float(self.paid_amount) if self.paid_amount else 0,
            'balance_amount': float(self.balance_amount) if self.balance_amount else 0,
            'due_date': self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            'status': self.status,
            'payment_terms': self.payment_terms,
            'invoice_number': self.invoice_number,
            'invoice_date': self.invoice_date.strftime('%Y-%m-%d') if self.invoice_date else None,
            'invoice_amount': float(self.invoice_amount) if self.invoice_amount else 0,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }


class PaymentRecord(StandardModel):
    """付款记录表"""
    __tablename__ = 'payment_records'

    payment_number = db.Column(db.String(50), nullable=False)
    area_id = db.Column(db.Integer, db.ForeignKey('administrative_areas.id'), nullable=False)
    payment_date = db.Column(db.Date, nullable=False)
    amount = db.Column(db.Numeric(12, 2), nullable=False)
    payment_method = db.Column(db.String(30), nullable=False)  # 现金/银行转账/支票/其他
    payable_id = db.Column(db.Integer, db.ForeignKey('account_payables.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    bank_account = db.Column(db.String(50), nullable=True)
    reference_number = db.Column(db.String(50), nullable=True)
    voucher_id = db.Column(db.Integer, db.ForeignKey('financial_vouchers.id'), nullable=True)
    summary = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='已确认')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    reviewed_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    reviewed_at = db.Column(DATETIME2(precision=1), nullable=True)
    notes = db.Column(db.Text, nullable=True)

    # 关系
    area = db.relationship('AdministrativeArea', backref='payment_records')
    supplier = db.relationship('Supplier', backref='payment_records')
    voucher = db.relationship('FinancialVoucher', backref='payment_records')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_payments')
    reviewer = db.relationship('User', foreign_keys=[reviewed_by], backref='reviewed_payments')

    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('area_id', 'payment_number', name='uq_payment_records_area_number'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'payment_number': self.payment_number,
            'area_id': self.area_id,
            'payment_date': self.payment_date.strftime('%Y-%m-%d') if self.payment_date else None,
            'amount': float(self.amount) if self.amount else 0,
            'payment_method': self.payment_method,
            'payable_id': self.payable_id,
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else '',
            'bank_account': self.bank_account,
            'reference_number': self.reference_number,
            'voucher_id': self.voucher_id,
            'summary': self.summary,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
