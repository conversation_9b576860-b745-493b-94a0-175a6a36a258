"""
财务报表路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app, make_response
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import AccountingSubject, FinancialVoucher, VoucherDetail, AccountPayable, PaymentRecord
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text, func
from datetime import datetime, date, timedelta
import json


@financial_bp.route('/reports')
@login_required
@school_required
@check_permission('财务报表', 'view')
def reports_index(user_area):
    """财务报表首页"""
    return render_template('financial/reports/index.html')


@financial_bp.route('/reports/balance-sheet')
@login_required
@school_required
@check_permission('财务报表', 'view')
def balance_sheet():
    """资产负债表"""
    user_area = current_user.get_current_area()
    
    # 获取查询参数
    report_date = request.args.get('report_date', date.today().strftime('%Y-%m-%d'))
    
    try:
        report_date_obj = datetime.strptime(report_date, '%Y-%m-%d').date()
    except ValueError:
        report_date_obj = date.today()
        report_date = report_date_obj.strftime('%Y-%m-%d')
    
    # 查询各类科目余额
    # 这里简化处理，实际应该根据凭证明细计算余额
    assets = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='资产',
        is_active=True
    ).order_by(AccountingSubject.code).all()
    
    liabilities = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='负债',
        is_active=True
    ).order_by(AccountingSubject.code).all()
    
    equity = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='所有者权益',
        is_active=True
    ).order_by(AccountingSubject.code).all()
    
    # 计算各科目余额（简化版本）
    subject_balances = {}
    
    # 查询应付账款余额
    payable_balance = db.session.query(
        func.sum(AccountPayable.balance_amount)
    ).filter_by(area_id=user_area.id).scalar() or 0
    
    # 为应付账款科目设置余额
    for subject in liabilities:
        if subject.code == '2201':  # 应付账款
            subject_balances[subject.id] = float(payable_balance)
        else:
            subject_balances[subject.id] = 0
    
    # 其他科目余额设为0（实际应该从凭证明细计算）
    for subject in assets + equity:
        subject_balances[subject.id] = 0
    
    return render_template('financial/reports/balance_sheet.html',
                         assets=assets,
                         liabilities=liabilities,
                         equity=equity,
                         subject_balances=subject_balances,
                         report_date=report_date)


@financial_bp.route('/reports/income-statement')
@login_required
@school_required
@check_permission('财务报表', 'view')
def income_statement():
    """利润表"""
    user_area = current_user.get_current_area()
    
    # 获取查询参数
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    
    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        end_date_obj = date.today()
        start_date_obj = end_date_obj.replace(day=1)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = end_date_obj.strftime('%Y-%m-%d')
    
    # 查询收入和费用科目
    income_subjects = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='收入',
        is_active=True
    ).order_by(AccountingSubject.code).all()
    
    expense_subjects = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='费用',
        is_active=True
    ).order_by(AccountingSubject.code).all()
    
    # 计算期间发生额（简化版本，实际应该从凭证明细计算）
    subject_amounts = {}
    
    # 查询期间凭证
    vouchers = FinancialVoucher.query.filter(
        FinancialVoucher.area_id == user_area.id,
        FinancialVoucher.voucher_date >= start_date_obj,
        FinancialVoucher.voucher_date <= end_date_obj,
        FinancialVoucher.status.in_(['已审核', '已记账'])
    ).all()
    
    voucher_ids = [v.id for v in vouchers]
    
    if voucher_ids:
        # 查询凭证明细
        details = VoucherDetail.query.filter(
            VoucherDetail.voucher_id.in_(voucher_ids)
        ).all()
        
        # 计算各科目发生额
        for detail in details:
            subject_id = detail.subject_id
            if subject_id not in subject_amounts:
                subject_amounts[subject_id] = {'debit': 0, 'credit': 0}
            
            subject_amounts[subject_id]['debit'] += float(detail.debit_amount or 0)
            subject_amounts[subject_id]['credit'] += float(detail.credit_amount or 0)
    
    # 计算各科目净额
    subject_net_amounts = {}
    for subject in income_subjects + expense_subjects:
        if subject.id in subject_amounts:
            amounts = subject_amounts[subject.id]
            if subject.subject_type == '收入':
                # 收入科目：贷方-借方
                subject_net_amounts[subject.id] = amounts['credit'] - amounts['debit']
            else:
                # 费用科目：借方-贷方
                subject_net_amounts[subject.id] = amounts['debit'] - amounts['credit']
        else:
            subject_net_amounts[subject.id] = 0
    
    return render_template('financial/reports/income_statement.html',
                         income_subjects=income_subjects,
                         expense_subjects=expense_subjects,
                         subject_amounts=subject_net_amounts,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/reports/payables-aging')
@login_required
@school_required
@check_permission('财务报表', 'view')
def payables_aging():
    """应付账款账龄分析"""
    user_area = current_user.get_current_area()
    
    # 获取所有未付清的应付账款
    payables = AccountPayable.query.filter(
        AccountPayable.area_id == user_area.id,
        AccountPayable.balance_amount > 0
    ).order_by(AccountPayable.created_at.desc()).all()
    
    # 计算账龄
    today = date.today()
    aging_data = []
    
    for payable in payables:
        created_date = payable.created_at.date() if payable.created_at else today
        days_outstanding = (today - created_date).days
        
        # 分类账龄
        if days_outstanding <= 30:
            aging_category = '30天以内'
        elif days_outstanding <= 60:
            aging_category = '31-60天'
        elif days_outstanding <= 90:
            aging_category = '61-90天'
        else:
            aging_category = '90天以上'
        
        aging_data.append({
            'payable': payable,
            'days_outstanding': days_outstanding,
            'aging_category': aging_category
        })
    
    # 按账龄分组统计
    aging_summary = {
        '30天以内': {'count': 0, 'amount': 0},
        '31-60天': {'count': 0, 'amount': 0},
        '61-90天': {'count': 0, 'amount': 0},
        '90天以上': {'count': 0, 'amount': 0}
    }
    
    for item in aging_data:
        category = item['aging_category']
        aging_summary[category]['count'] += 1
        aging_summary[category]['amount'] += float(item['payable'].balance_amount)
    
    return render_template('financial/reports/payables_aging.html',
                         aging_data=aging_data,
                         aging_summary=aging_summary)


@financial_bp.route('/reports/voucher-summary')
@login_required
@school_required
@check_permission('财务报表', 'view')
def voucher_summary():
    """凭证汇总表"""
    user_area = current_user.get_current_area()
    
    # 获取查询参数
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    
    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        end_date_obj = date.today()
        start_date_obj = end_date_obj.replace(day=1)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = end_date_obj.strftime('%Y-%m-%d')
    
    # 查询期间凭证统计
    voucher_stats = db.session.query(
        FinancialVoucher.voucher_type,
        FinancialVoucher.status,
        func.count(FinancialVoucher.id).label('count'),
        func.sum(FinancialVoucher.total_amount).label('total_amount')
    ).filter(
        FinancialVoucher.area_id == user_area.id,
        FinancialVoucher.voucher_date >= start_date_obj,
        FinancialVoucher.voucher_date <= end_date_obj
    ).group_by(
        FinancialVoucher.voucher_type,
        FinancialVoucher.status
    ).all()
    
    # 整理统计数据
    summary_data = {}
    for voucher_type, status, count, total_amount in voucher_stats:
        if voucher_type not in summary_data:
            summary_data[voucher_type] = {}
        
        summary_data[voucher_type][status] = {
            'count': count,
            'total_amount': float(total_amount) if total_amount else 0
        }
    
    return render_template('financial/reports/voucher_summary.html',
                         summary_data=summary_data,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/reports/export/<report_type>')
@login_required
@school_required
@check_permission('财务报表', 'export')
def export_report(report_type):
    """导出报表"""
    # 这里可以实现Excel导出功能
    # 暂时返回JSON格式
    user_area = current_user.get_current_area()
    
    if report_type == 'payables':
        # 导出应付账款
        payables = AccountPayable.query.filter_by(area_id=user_area.id).all()
        data = [payable.to_dict() for payable in payables]
    elif report_type == 'payments':
        # 导出付款记录
        payments = PaymentRecord.query.filter_by(area_id=user_area.id).all()
        data = [payment.to_dict() for payment in payments]
    else:
        data = []
    
    response = make_response(json.dumps(data, ensure_ascii=False, indent=2))
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename={report_type}_report.json'
    
    return response
