-- 为 stock_ins 表添加财务相关字段
-- 执行前请备份数据库

USE [StudentsCMSSP]
GO

-- 添加财务相关字段
ALTER TABLE stock_ins ADD 
    is_financial_confirmed BIT NOT NULL DEFAULT 0,
    total_cost DECIMAL(10,2) NULL,
    supplier_id INT NULL,
    payable_id INT NULL,
    voucher_id INT NULL,
    area_id INT NULL;
GO

-- 添加外键约束
ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_supplier_id 
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
GO

ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_area_id 
    FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
GO

-- 为现有数据设置 area_id（从 warehouse 表获取）
UPDATE si 
SET area_id = w.area_id
FROM stock_ins si
INNER JOIN warehouses w ON si.warehouse_id = w.id
WHERE si.area_id IS NULL;
GO

-- 为现有数据计算 total_cost
UPDATE si
SET total_cost = (
    SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
)
WHERE si.total_cost IS NULL;
GO

-- 为现有数据设置 supplier_id（从第一个入库明细获取）
UPDATE si
SET supplier_id = (
    SELECT TOP 1 sii.supplier_id
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
    AND sii.supplier_id IS NOT NULL
)
WHERE si.supplier_id IS NULL;
GO

-- 创建索引以提高查询性能
CREATE INDEX IX_stock_ins_is_financial_confirmed ON stock_ins(is_financial_confirmed);
CREATE INDEX IX_stock_ins_supplier_id ON stock_ins(supplier_id);
CREATE INDEX IX_stock_ins_area_id ON stock_ins(area_id);
CREATE INDEX IX_stock_ins_payable_id ON stock_ins(payable_id);
CREATE INDEX IX_stock_ins_voucher_id ON stock_ins(voucher_id);
GO

PRINT '财务字段添加完成！';
GO
