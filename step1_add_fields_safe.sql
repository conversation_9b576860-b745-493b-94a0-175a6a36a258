-- 第一步：安全地添加字段到 stock_ins 表（检查字段是否存在）
-- 请在 SQL Server Management Studio 中执行此脚本

USE [StudentsCMSSP]
GO

PRINT '开始检查并添加字段到 stock_ins 表...'

-- 添加 area_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'area_id')
BEGIN
    ALTER TABLE stock_ins ADD area_id INT NULL;
    PRINT '✓ 添加 area_id 字段'
END
ELSE
BEGIN
    PRINT '- area_id 字段已存在'
END

-- 添加 supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_ins ADD supplier_id INT NULL;
    PRINT '✓ 添加 supplier_id 字段'
END
ELSE
BEGIN
    PRINT '- supplier_id 字段已存在'
END

-- 添加 payable_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'payable_id')
BEGIN
    ALTER TABLE stock_ins ADD payable_id INT NULL;
    PRINT '✓ 添加 payable_id 字段'
END
ELSE
BEGIN
    PRINT '- payable_id 字段已存在'
END

-- 添加 voucher_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'voucher_id')
BEGIN
    ALTER TABLE stock_ins ADD voucher_id INT NULL;
    PRINT '✓ 添加 voucher_id 字段'
END
ELSE
BEGIN
    PRINT '- voucher_id 字段已存在'
END

-- 添加 is_financial_confirmed 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'is_financial_confirmed')
BEGIN
    ALTER TABLE stock_ins ADD is_financial_confirmed BIT NOT NULL DEFAULT 0;
    PRINT '✓ 添加 is_financial_confirmed 字段'
END
ELSE
BEGIN
    PRINT '- is_financial_confirmed 字段已存在'
END

-- 添加 total_cost 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'total_cost')
BEGIN
    ALTER TABLE stock_ins ADD total_cost DECIMAL(10,2) NULL;
    PRINT '✓ 添加 total_cost 字段'
END
ELSE
BEGIN
    PRINT '- total_cost 字段已存在'
END

-- 添加 financial_confirmed_at 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_at')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_at DATETIME2(1) NULL;
    PRINT '✓ 添加 financial_confirmed_at 字段'
END
ELSE
BEGIN
    PRINT '- financial_confirmed_at 字段已存在'
END

-- 添加 financial_confirmed_by 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_by')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_by INT NULL;
    PRINT '✓ 添加 financial_confirmed_by 字段'
END
ELSE
BEGIN
    PRINT '- financial_confirmed_by 字段已存在'
END

-- 显示当前字段状态
PRINT ''
PRINT '=== 当前 stock_ins 表字段状态 ==='
SELECT 
    COLUMN_NAME as 字段名,
    DATA_TYPE as 数据类型,
    IS_NULLABLE as 允许空值,
    COLUMN_DEFAULT as 默认值
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'stock_ins' 
AND COLUMN_NAME IN ('area_id', 'supplier_id', 'payable_id', 'voucher_id', 'is_financial_confirmed', 'total_cost', 'financial_confirmed_at', 'financial_confirmed_by')
ORDER BY COLUMN_NAME;

PRINT ''
PRINT '字段检查和添加完成！'
PRINT '请继续执行 step2_update_data.sql'

GO
