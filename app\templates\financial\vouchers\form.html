{% extends "financial/base.html" %}

{% block title %}
{% if voucher %}编辑财务凭证{% else %}新建财务凭证{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if voucher %}编辑财务凭证{% else %}新建财务凭证{% endif %}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.voucher_date.label(class="form-label") }}
                                    {{ form.voucher_date(class="form-control") }}
                                    {% if form.voucher_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.voucher_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.voucher_type.label(class="form-label") }}
                                    {{ form.voucher_type(class="form-control") }}
                                    {% if form.voucher_type.errors %}
                                        <div class="text-danger">
                                            {% for error in form.voucher_type.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            {{ form.summary.label(class="form-label") }}
                            {{ form.summary(class="form-control") }}
                            {% if form.summary.errors %}
                                <div class="text-danger">
                                    {% for error in form.summary.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                {% if voucher %}更新凭证{% else %}创建凭证{% endif %}
                            </button>
                            <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
