{% extends "financial/base.html" %}

{% block title %}财务凭证管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">财务凭证管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.create_voucher') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 新建凭证
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" 
                                           value="{{ keyword }}" placeholder="凭证号或摘要">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="voucher_type">凭证类型</label>
                                    <select class="form-control" id="voucher_type" name="voucher_type">
                                        <option value="">-- 所有类型 --</option>
                                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款凭证</option>
                                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款凭证</option>
                                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账凭证</option>
                                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账凭证</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">状态</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">-- 所有状态 --</option>
                                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="start_date">开始日期</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="end_date">结束日期</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-undo"></i> 重置
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 凭证列表 -->
                    {% if vouchers.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>凭证号</th>
                                    <th>日期</th>
                                    <th>类型</th>
                                    <th>摘要</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>创建人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for voucher in vouchers.items %}
                                <tr>
                                    <td>{{ voucher.voucher_number }}</td>
                                    <td>{{ voucher.voucher_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ voucher.voucher_type }}</td>
                                    <td>{{ voucher.summary }}</td>
                                    <td class="text-right">{{ "%.2f"|format(voucher.total_amount) }}</td>
                                    <td>
                                        {% if voucher.status == '草稿' %}
                                            <span class="badge badge-secondary">{{ voucher.status }}</span>
                                        {% elif voucher.status == '待审核' %}
                                            <span class="badge badge-warning">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已审核' %}
                                            <span class="badge badge-success">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已记账' %}
                                            <span class="badge badge-primary">{{ voucher.status }}</span>
                                        {% else %}
                                            <span class="badge badge-light">{{ voucher.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ voucher.creator.username if voucher.creator else '未知' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" 
                                               class="btn btn-info btn-sm" title="查看">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}" 
                                               class="btn btn-warning btn-sm" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            {% if voucher.status == '待审核' %}
                                            <form method="POST" action="{{ url_for('financial.review_voucher', id=voucher.id) }}" 
                                                  style="display: inline;" onsubmit="return confirm('确定要审核此凭证吗？')">
                                                <button type="submit" class="btn btn-success btn-sm" title="审核">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <form method="POST" action="{{ url_for('financial.delete_voucher', id=voucher.id) }}" 
                                                  style="display: inline;" onsubmit="return confirm('确定要删除此凭证吗？此操作不可恢复！')">
                                                <button type="submit" class="btn btn-danger btn-sm" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if vouchers.pages > 1 %}
                    <nav aria-label="凭证分页">
                        <ul class="pagination justify-content-center">
                            {% if vouchers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in vouchers.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != vouchers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if vouchers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 暂无财务凭证数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
