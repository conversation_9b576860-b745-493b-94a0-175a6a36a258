-- 为 stock_ins 表添加外键约束
-- 请在执行 fix_stock_ins_step_by_step.sql 之后执行此脚本

USE [StudentsCMSSP]
GO

PRINT '=== 添加外键约束 ==='

-- 检查并添加 area_id 外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_area_id')
BEGIN
    -- 检查是否有无效的 area_id 值
    IF NOT EXISTS (
        SELECT 1 FROM stock_ins si 
        LEFT JOIN administrative_areas aa ON si.area_id = aa.id 
        WHERE si.area_id IS NOT NULL AND aa.id IS NULL
    )
    BEGIN
        ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_area_id 
            FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
        PRINT '✓ 添加 area_id 外键约束'
    END
    ELSE
    BEGIN
        PRINT '⚠️ 存在无效的 area_id 值，请先清理数据'
        SELECT DISTINCT si.area_id, '无效的 area_id' as 问题
        FROM stock_ins si 
        LEFT JOIN administrative_areas aa ON si.area_id = aa.id 
        WHERE si.area_id IS NOT NULL AND aa.id IS NULL
    END
END
ELSE
BEGIN
    PRINT '- area_id 外键约束已存在'
END

-- 检查并添加 supplier_id 外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_supplier_id')
BEGIN
    -- 检查是否有无效的 supplier_id 值
    IF NOT EXISTS (
        SELECT 1 FROM stock_ins si 
        LEFT JOIN suppliers s ON si.supplier_id = s.id 
        WHERE si.supplier_id IS NOT NULL AND s.id IS NULL
    )
    BEGIN
        ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_supplier_id 
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
        PRINT '✓ 添加 supplier_id 外键约束'
    END
    ELSE
    BEGIN
        PRINT '⚠️ 存在无效的 supplier_id 值，请先清理数据'
        SELECT DISTINCT si.supplier_id, '无效的 supplier_id' as 问题
        FROM stock_ins si 
        LEFT JOIN suppliers s ON si.supplier_id = s.id 
        WHERE si.supplier_id IS NOT NULL AND s.id IS NULL
    END
END
ELSE
BEGIN
    PRINT '- supplier_id 外键约束已存在'
END

PRINT '=== 外键约束添加完成 ==='

-- 显示当前 stock_ins 表结构
PRINT ''
PRINT '=== 当前 stock_ins 表结构 ==='
SELECT 
    c.COLUMN_NAME as 字段名,
    c.DATA_TYPE as 数据类型,
    CASE WHEN c.IS_NULLABLE = 'YES' THEN '是' ELSE '否' END as 允许空值,
    ISNULL(c.COLUMN_DEFAULT, '') as 默认值
FROM INFORMATION_SCHEMA.COLUMNS c
WHERE c.TABLE_NAME = 'stock_ins'
ORDER BY c.ORDINAL_POSITION

PRINT ''
PRINT '🎉 所有修复完成！现在可以使用财务模块了。'

GO
