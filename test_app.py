#!/usr/bin/env python
"""
测试应用程序启动脚本
"""

import sys
import os

# 清除模块缓存
modules_to_remove = [k for k in sys.modules.keys() if k.startswith('app.')]
for module in modules_to_remove:
    if module in sys.modules:
        del sys.modules[module]

try:
    from app import create_app
    
    app = create_app()
    print("✅ 应用程序创建成功")
    
    # 测试路由注册
    with app.app_context():
        print("✅ 应用程序上下文正常")
        
        # 列出所有路由
        print("\n📋 已注册的路由:")
        for rule in app.url_map.iter_rules():
            if 'accounting-subjects' in rule.rule:
                print(f"  - {rule.rule} -> {rule.endpoint}")
    
    print("\n🚀 启动 Flask 开发服务器...")
    app.run(host='127.0.0.1', port=8080, debug=True)
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
