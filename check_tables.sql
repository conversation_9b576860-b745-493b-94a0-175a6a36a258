-- 检查数据库中的表结构
USE [StudentsCMSSP]
GO

PRINT '检查相关表是否存在...'
PRINT ''

-- 检查 menu_plans 表是否存在
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'menu_plans')
BEGIN
    PRINT '✓ menu_plans 表存在'
    SELECT COUNT(*) as menu_plans_count FROM menu_plans
END
ELSE
BEGIN
    PRINT '❌ menu_plans 表不存在'
END

-- 检查 weekly_menus 表是否存在
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menus')
BEGIN
    PRINT '✓ weekly_menus 表存在'
    SELECT COUNT(*) as weekly_menus_count FROM weekly_menus
END
ELSE
BEGIN
    PRINT '❌ weekly_menus 表不存在'
END

-- 检查 consumption_plans 表结构
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_plans')
BEGIN
    PRINT '✓ consumption_plans 表存在'
    
    -- 检查是否有 menu_plan_id 字段
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'menu_plan_id')
    BEGIN
        PRINT '  - 有 menu_plan_id 字段'
    END
    ELSE
    BEGIN
        PRINT '  - 没有 menu_plan_id 字段'
    END
    
    -- 检查是否有 area_id 字段
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'area_id')
    BEGIN
        PRINT '  - 有 area_id 字段'
    END
    ELSE
    BEGIN
        PRINT '  - 没有 area_id 字段'
    END
    
    SELECT COUNT(*) as consumption_plans_count FROM consumption_plans
END
ELSE
BEGIN
    PRINT '❌ consumption_plans 表不存在'
END

PRINT ''
PRINT '表检查完成！'

GO
