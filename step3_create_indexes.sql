-- 第三步：创建索引
-- 请在执行 step2_update_data.sql 之后执行此脚本

USE [StudentsCMSSP]
GO

PRINT '开始创建索引...'

-- 创建 is_financial_confirmed 索引
CREATE INDEX IX_stock_ins_is_financial_confirmed ON stock_ins(is_financial_confirmed);
PRINT '✓ 创建 is_financial_confirmed 索引'

-- 创建 supplier_id 索引
CREATE INDEX IX_stock_ins_supplier_id ON stock_ins(supplier_id);
PRINT '✓ 创建 supplier_id 索引'

-- 创建 area_id 索引
CREATE INDEX IX_stock_ins_area_id ON stock_ins(area_id);
PRINT '✓ 创建 area_id 索引'

-- 创建 payable_id 索引
CREATE INDEX IX_stock_ins_payable_id ON stock_ins(payable_id);
PRINT '✓ 创建 payable_id 索引'

-- 创建 voucher_id 索引
CREATE INDEX IX_stock_ins_voucher_id ON stock_ins(voucher_id);
PRINT '✓ 创建 voucher_id 索引'

PRINT '所有索引创建完成！'

-- 最终验证
PRINT ''
PRINT '=== 最终验证结果 ==='
SELECT 
    COUNT(*) as 入库单总数,
    COUNT(area_id) as 有area_id的记录数,
    COUNT(supplier_id) as 有supplier_id的记录数,
    COUNT(total_cost) as 有total_cost的记录数,
    SUM(CASE WHEN is_financial_confirmed = 1 THEN 1 ELSE 0 END) as 已财务确认数量,
    AVG(total_cost) as 平均成本
FROM stock_ins;

PRINT ''
PRINT '🎉 stock_ins 表修复完全完成！'
PRINT '现在可以正常使用财务模块了。'

GO
